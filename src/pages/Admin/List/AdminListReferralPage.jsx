import React from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { Navigate, useNavigate } from "react-router-dom";
import { LazyLoad } from "Components/LazyLoad";
import { ModalSidebar } from "Components/ModalSidebar";
import { MkdListTableV2 } from "Components/MkdListTable";
import {
  AdminEditReferralPage,
  AdminAddReferralPage,
} from "Src/routes/LazyLoad";
import "./AdminListReferralPage.css";
import { AiFillEye } from "react-icons/ai";
import { EditIcon2, TrashIcon } from "Assets/svgs";

let sdk = new MkdSDK();

const columns = [
  {
    header: "ID",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Opportunity Title",
    accessor: "title",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Date Posted",
    accessor: "created_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Posted By",
    accessor: "user_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Type",
    accessor: "type",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      // Map types to the ones in the screenshot
      const typeMap = {
        "full_time": "full_time",
        "part_time": "part_time",
        "contract": "contract",
        "freelance": "contract",
        "internship": "part_time"
      };
      return typeMap[value] || value;
    }
  },
  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      // Map statuses to the ones in the screenshot with appropriate styling
      const statusClasses = {
        "completed": "status completed",
        "active": "status active",
        "deleted": "status deleted"
      };
      return `<span class="${statusClasses[value] || 'status'}">${value}</span>`;
    }
  },
  {
    header: "Action",
    accessor: "",
  },
];

const AdminReferralListPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const [searchValue, setSearchValue] = React.useState("");
  // Create a ref for the refresh button
  const refreshRef = React.useRef(null);

  const [selectedItems, setSelectedItems] = React.useState([]);
  const [stats, setStats] = React.useState({
    total: 0,
    active: 0,
    completed: 0
  });

  // Store all referrals and filtered referrals (following User Dashboard pattern)
  const [allReferrals, setAllReferrals] = React.useState([]);
  const [referrals, setReferrals] = React.useState([]);

  // Function to fetch dashboard stats
  const fetchDashboardStats = async () => {
    try {
      const sdk = new MkdSDK();
      const result = await sdk.callRawAPI("/v1/api/dealmaker/super_admin/dashboard", {}, "GET");

      if (!result.error && result.model) {
        setStats({
          total: result.model.total_referrals || 0,
          active: result.model.total_active_referrals || 0,
          completed: result.model.total_completed_referrals || 0
        });
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
    }
  };

  // Function to fetch referral data from API
  const fetchReferralData = async () => {
    try {
      console.log("📡 Fetching referral data from API...");
      setTableData(prev => ({...prev, loading: true}));
      const sdk = new MkdSDK();
      sdk.setTable("referral");
      const result = await sdk.callRestAPI({}, "GETALL");

      console.log("📡 API result:", result);

      if (!result.error && result.list) {
        const referralData = result.list;
        console.log("✅ Successfully fetched", referralData.length, "referrals");
        console.log("📋 Referral data:", referralData);

        setAllReferrals(referralData);
        setReferrals(referralData);

        setTableData(prev => ({
          ...prev,
          data: referralData,
          loading: false,
          total: referralData.length,
          pages: Math.ceil(referralData.length / prev.limit)
        }));
      } else {
        console.error("❌ Error fetching referral data:", result?.error);
        setTableData(prev => ({...prev, loading: false}));
      }
    } catch (error) {
      console.error("❌ Exception fetching referral data:", error);
      setTableData(prev => ({...prev, loading: false}));
    }
  };

  // Client-side search function (following User Dashboard pattern)
  const handleSearch = (query) => {
    console.log("🔍 handleSearch called with query:", query);
    console.log("📊 allReferrals length:", allReferrals.length);
    console.log("📋 allReferrals data:", allReferrals);

    if (!query.trim()) {
      console.log("✅ Empty query, showing all referrals");
      setReferrals(allReferrals);
      setTableData(prev => ({
        ...prev,
        data: allReferrals,
        total: allReferrals.length,
        pages: Math.ceil(allReferrals.length / prev.limit)
      }));
      return;
    }

    const searchLower = query.toLowerCase();
    console.log("🔎 Searching for:", searchLower);

    // Filter referrals based on search query (search by title, type, status)
    const filtered = allReferrals.filter(referral => {
      console.log("🔍 Checking referral:", referral);

      // Search by title (both title and job_title fields)
      const titleMatch = (referral.title && referral.title.toLowerCase().includes(searchLower)) ||
                        (referral.job_title && referral.job_title.toLowerCase().includes(searchLower));

      // Search by type
      const typeMatch = referral.type && referral.type.toLowerCase().includes(searchLower);

      // Search by status
      const statusMatch = referral.status && referral.status.toLowerCase().includes(searchLower);

      const matches = titleMatch || typeMatch || statusMatch;
      console.log(`📝 Referral ${referral.id}: title="${referral.title}", type="${referral.type}", status="${referral.status}" - matches: ${matches}`);

      return matches;
    });

    console.log("✨ Filtered results:", filtered.length, "items");
    console.log("📋 Filtered data:", filtered);

    setReferrals(filtered);
    setTableData(prev => ({
      ...prev,
      data: filtered,
      total: filtered.length,
      pages: Math.ceil(filtered.length / prev.limit)
    }));
  };

  // Fetch dashboard stats and referral data when component mounts
  React.useEffect(() => {
    fetchDashboardStats();
    fetchReferralData();
  }, []);

  // Handle search when searchValue changes (following User Dashboard pattern)
  React.useEffect(() => {
    console.log("🎯 Search useEffect triggered! searchValue:", searchValue);
    console.log("📊 allReferrals in useEffect:", allReferrals.length, "items");
    handleSearch(searchValue);
  }, [searchValue, allReferrals]);


  // Listen for REFRESH_DATA action to refresh the table
  React.useEffect(() => {
    const handleRefresh = (e) => {
      if (e.detail?.type === "REFRESH_DATA" && refreshRef.current) {
        console.log("Refreshing table data...", e.detail);

        // Set loading state to true immediately
        setTableData(prev => ({...prev, loading: true}));

        // Fetch the latest data from API
        const fetchData = async () => {
          try {
            console.log("Fetching fresh data...");
            await fetchReferralData();
            await fetchDashboardStats();
            console.log("Data refreshed successfully");
          } catch (error) {
            console.error("Error refreshing data:", error);
            setTableData(prev => ({...prev, loading: false}));
          }
        };

        // Execute the data fetch
        fetchData();
      }
    };

    // Add event listener for custom events
    window.addEventListener("globalStateChange", handleRefresh);

    return () => {
      window.removeEventListener("globalStateChange", handleRefresh);
    };
  }, []);

  // State to manage table data and loading
  const [tableData, setTableData] = React.useState({
    data: [],
    loading: true,
    page: 1,
    limit: 10,
    pages: 0,
    total: 0
  });

  const onToggleModal = async (modal, toggle, ids = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setActiveEditId(ids[0]);
        break;
      case "delete":
        await handleDelete(ids);
        break;
      default:
        break;
    }
  };

  // Function to handle delete action

  const handleDelete = async (ids) => {
    try {
      console.log("Deleting item with ID:", ids[0]);

      // Set loading state to true
      setTableData(prev => ({...prev, loading: true}));

      sdk.setTable("referral");
      const result = await sdk.callRestAPI({ id: ids[0] }, "DELETE");

      console.log("Delete result:", result);

      if (!result?.error) {
        // Refresh the data from API after successful deletion
        await fetchReferralData();
        await fetchDashboardStats();
      } else {
        // If there was an error, just set loading to false
        setTableData(prev => ({...prev, loading: false}));
      }
    } catch (error) {
      console.error("Error deleting item:", error);
      // Make sure to set loading to false in case of error
      setTableData(prev => ({...prev, loading: false}));
    }
  };


  return (
    <>
      <div className="opportunities-dashboard bg-[#1E1E1E]">
        <div className="container">
          {/* Header */}
          <div className="header">
            <h1>Opportunities Dashboard</h1>
            <p>Track and manage your opportunities in real-time</p>
          </div>

          {/* Search and Add Button */}
          <div className="search-add">
            <div className="search-container">
              <div className="search-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search"
                value={searchValue}
                onChange={(e) => {
                  console.log("⌨️ Search input changed to:", e.target.value);
                  setSearchValue(e.target.value);
                }}
                className="search-input"
              />
            </div>
            <button
              onClick={() => onToggleModal("add", true)}
              className="add-button"
            >
              <span>+</span> New Opportunity
            </button>
          </div>

          {/* Stats Cards */}
          <div className="stats-grid">
            {/* Total Opportunities */}
            <div className="stat-card">
              <div className="stat-card-content">
                <div>
                  <div className="stat-card-title">Total Opportunities</div>
                  <div className="stat-card-value">{stats.total}</div>
                </div>
                <div className="stat-card-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Active Opportunities */}
            <div className="stat-card">
              <div className="stat-card-content">
                <div>
                  <div className="stat-card-title">Active Opportunities</div>
                  <div className="stat-card-value active">{stats.active}</div>
                </div>
                <div className="stat-card-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Completed Opportunities */}
            <div className="stat-card">
              <div className="stat-card-content">
                <div>
                  <div className="stat-card-title">Completed Opportunities</div>
                  <div className="stat-card-value">{stats.completed}</div>
                </div>
                <div className="stat-card-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Table */}
          <div className="table-container">
            <LazyLoad>
              <MkdListTableV2
                columns={columns}
                tableRole={"admin"}
                table={"referral"}
                actionId={"id"}
                actions={{
                  view: {
                    show: true,
                    action: (ids) => {
                      console.log("View item with ID:", ids[0]);
                      navigate(`/admin/view-referral/${ids[0]}`);
                      // Navigation to view referral page
                    },
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "View",
                    icon: <AiFillEye className="text-blue-500" />
                  },
                  edit: {
                    show: true,
                    multiple: false,
                    action: (ids) => onToggleModal("edit", true, ids),
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Edit",
                    icon: <EditIcon2 stroke="#4CAF50" />
                  },
                  delete: {
                    show: true,
                    action: (ids) => onToggleModal("delete", true, ids),
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Delete",
                    icon: <TrashIcon fill="#E53E3E" />
                  },
                  select: { show: false, action: null, multiple: false },
                  add: {
                    show: true,
                    action: () => onToggleModal("add", true),
                    multiple: false,
                    children: "Add New",
                    showChildren: true,
                  },
                  export: { show: false, action: null, multiple: true },
                }}
                selectedItems={selectedItems}
                setSelectedItems={setSelectedItems}
                refreshRef={refreshRef}
                actionPosition={["buttons"]}
                allowSortColumns={false}
                // Sample data to match the screenshot
                externalData={{
                  use: true,
                  data: tableData.data,
                  loading: tableData.loading,
                  page: tableData.page,
                  limit: tableData.limit,
                  pages: tableData.pages,
                  total: tableData.total,
                  fetch: async (page, limit, filter) => {
                    // This function is called when the table needs to refresh
                    console.log("Fetch called with page:", page, "limit:", limit, "filter:", filter);
                    await fetchReferralData();
                  },
                  search: async (search, _columns, _searchFilter, query) => {
                    // This function is called when the search is triggered from the table
                    console.log("🔍 externalData.search called with:", search, query);
                    console.log("🔍 _columns:", _columns);
                    console.log("🔍 _searchFilter:", _searchFilter);

                    // Update the search value which will trigger client-side filtering
                    setSearchValue(search || "");
                  }
                }}
              />
            </LazyLoad>
          </div>

          {/* Pagination */}
          <div className="pagination">
            <div className="pagination-info">
              Showing 1 to {Math.min(tableData.limit, tableData.total)} of {tableData.total} entries
              {searchValue && ` (filtered from ${allReferrals.length} total entries)`}
            </div>
            <div className="pagination-buttons">
              <button className="pagination-button prev">Previous</button>
              <button className="pagination-button next">Next</button>
            </div>
          </div>
        </div>
      </div>

      <LazyLoad>
        <ModalSidebar
          isModalActive={showAddSidebar}
          closeModalFn={() => setShowAddSidebar(false)}
        >
          <AdminAddReferralPage setSidebar={setShowAddSidebar} />
        </ModalSidebar>
      </LazyLoad>

      {showEditSidebar && (
        <LazyLoad>
          <ModalSidebar
            isModalActive={showEditSidebar}
            closeModalFn={() => setShowEditSidebar(false)}
          >
            <AdminEditReferralPage
              activeId={activeEditId}
              setSidebar={setShowEditSidebar}
            />
          </ModalSidebar>
        </LazyLoad>
      )}


    </>
  );
};

export default AdminReferralListPage;
