import React, {
  useEffect,
  useContext,
  useState,
  useCallback,
  lazy,
  Suspense,
} from "react";
import { Routes, Route, useNavigate } from "react-router-dom";
import { AuthContext } from "Context/Auth";

import PrivateRoute from "./PrivateRoutes";
import PublicRoute from "./PublicRoutes";
import { PublicWrapper } from "Components/PublicWrapper";
import { NotFoundPage } from "Pages/404";
import { SnackBar } from "Components/SnackBar";
import { SessionExpiredModal } from "Components/SessionExpiredModal";

// generatePagesRoutes
import { UserWrapper } from "Components/UserWrapper";
import { AdminWrapper } from "Components/AdminWrapper";
import CommunityRequestsPage from "Pages/User/Communities/CommunityRequestsPage";

import {
  PublicPage,
  AdminListUserPage,
  AdminAddUserPage,
  AdminEditUserPage,
  AdminViewUserPage,
  AdminListUploadsPage,
  AdminAddUploadsPage,
  AdminEditUploadsPage,
  AdminViewUploadsPage,
  AdminListJobPage,
  AdminAddJobPage,
  AdminEditJobPage,
  AdminViewJobPage,
  AdminListTokensPage,
  AdminAddTokensPage,
  AdminEditTokensPage,
  AdminViewTokensPage,
  AdminListPreferencePage,
  AdminAddPreferencePage,
  AdminEditPreferencePage,
  AdminViewPreferencePage,
  AdminListStripe_productPage,
  AdminAddStripe_productPage,
  AdminEditStripe_productPage,
  AdminViewStripe_productPage,
  AdminListStripe_pricePage,
  AdminAddStripe_pricePage,
  AdminEditStripe_pricePage,
  AdminViewStripe_pricePage,
  AdminListCommunityPage,
  AdminAddCommunityPage,
  AdminEditCommunityPage,
  AdminViewCommunityPage,
  AdminListIndustryPage,
  AdminAddIndustryPage,
  AdminEditIndustryPage,
  AdminViewIndustryPage,
  AdminListCommunity_memberPage,
  AdminAddCommunity_memberPage,
  AdminEditCommunity_memberPage,
  AdminViewCommunity_memberPage,
  AdminListCommunity_join_requestPage,
  AdminAddCommunity_join_requestPage,
  AdminEditCommunity_join_requestPage,
  AdminViewCommunity_join_requestPage,
  AdminListCommunity_invitePage,
  AdminAddCommunity_invitePage,
  AdminEditCommunity_invitePage,
  AdminViewCommunity_invitePage,
  AdminListReferralPage,
  AdminAddReferralPage,
  AdminEditReferralPage,
  AdminViewReferralPage,
  AdminListReferral_communitiesPage,
  AdminAddReferral_communitiesPage,
  AdminEditReferral_communitiesPage,
  AdminViewReferral_communitiesPage,
  AdminListActivity_feedPage,
  AdminAddActivity_feedPage,
  AdminEditActivity_feedPage,
  AdminViewActivity_feedPage,
  AdminListMeetingsPage,
  AdminAddMeetingsPage,
  AdminEditMeetingsPage,
  AdminViewMeetingsPage,
  AdminListMeeting_attendeePage,
  AdminAddMeeting_attendeePage,
  AdminEditMeeting_attendeePage,
  AdminViewMeeting_attendeePage,
  AdminListIntegrationPage,
  AdminAddIntegrationPage,
  AdminEditIntegrationPage,
  AdminViewIntegrationPage,
  AdminListAnalytics_eventsPage,
  AdminListPaymentsPage,
  AdminListPlansPage,
  AdminViewPlansPage,
  AdminAddAnalytics_eventsPage,
  AdminEditAnalytics_eventsPage,
  AdminViewAnalytics_eventsPage,
  AdminListCommissionPage,
  AdminAddCommissionPage,
  AdminEditCommissionPage,
  AdminViewCommissionPage,
  AdminViewPaymentPage,
  AdminSettingsPage,
  MagicLoginVerifyPage,
  UserSignUpPage,
  UserResetPage,
  UserLoginPage,
} from "./LazyLoad";

import { LazyLoad } from "Components/LazyLoad";
import { RouteChangeModal } from "Components/RouteChangeModal";
import { useContexts } from "Hooks/useContexts";
import ChatListPage from "Pages/User/Chat/ChatListPage";
import ChatPage from "Pages/User/Chat/ChatPage";
import CreateCommunityPage from "Pages/User/Communities/CreateCommunityPage";
import JoinCommunityPage from "Pages/User/Communities/JoinCommunityPage";
import ReferralRecommendPage from "Pages/User/Referrals/ReferralRecommendPage";
import CallbackPage from "Pages/User/Auth/CallbackPage";
import ViewUserProfile from "Pages/User/Profile/ViewUserProfile";
import NotesPage from "Pages/User/Notes/NotesPage";

// Lazy load all user pages
const ForgotPasswordPage = lazy(() =>
  import("Pages/User/Auth/ForgotPasswordPage")
);
const UserDashboardPage = lazy(() =>
  import("Pages/User/Dashboard/UserDashboardPage")
);
const UserProfilePage = lazy(() =>
  import("Pages/User/Profile/UserProfilePage")
);
const UserSettingsPage = lazy(() =>
  import("Pages/User/Settings/UserSettingsPage")
);
const ReferralsPage = lazy(() => import("Pages/User/Referrals/ReferralsPage"));
const ReferralDetailsPage = lazy(() =>
  import("Pages/User/Referrals/ReferralDetailsPage")
);
const ReferralTimelinePage = lazy(() =>
  import("Pages/User/Referrals/ReferralTimelinePage")
);
const ProfileSettingsPage = lazy(() =>
  import("Pages/User/Profile/ProfileSettingsPage")
);
const ReferralFormPage = lazy(() =>
  import("Pages/User/Referrals/ReferralFormPage")
);
const ReferralFeedPage = lazy(() =>
  import("Pages/User/Referrals/ReferralFeedPage")
);
const CommunitiesPage = lazy(() =>
  import("Pages/User/Communities/CommunitiesPage")
);
const CommunityFormPage = lazy(() =>
  import("Pages/User/Communities/CommunityFormPage")
);
const PaymentModalPage = lazy(() =>
  import("Pages/User/Payment/PaymentModalPage")
);
const ReferralDetailPage = lazy(() =>
  import("Pages/User/Referrals/ReferralDetailPage")
);
const CommunityCreateStepTwoPage = lazy(() =>
  import("Pages/User/Communities/CommunityCreateStepTwoPage")
);
const CommunityCreateStepThreePage = lazy(() =>
  import("Pages/User/Communities/CommunityCreateStepThreePage")
);
const CommunityPrivacySettingsPage = lazy(() =>
  import("Pages/User/Communities/CommunityPrivacySettingsPage")
);
const CommunityUpgradePage = lazy(() =>
  import("Pages/User/Communities/CommunityUpgradePage")
);
const ReferralFeedDetailPage = lazy(() =>
  import("Pages/User/Referrals/ReferralFeedDetailPage")
);
const UserProfileEditPage = lazy(() =>
  import("Pages/User/Profile/UserProfileEditPage")
);
const ChatDetailPage = lazy(() => import("Pages/User/Chat/ChatDetailPage"));
const PaymentDashboardPage = lazy(() =>
  import("Pages/User/Payment/PaymentDashboardPage")
);
const BillingPlanPage = lazy(() =>
  import("Pages/User/Payment/BillingPlanPage")
);
const AffiliateProgramPage = lazy(() =>
  import("Pages/User/Affiliate/AffiliateProgramPage")
);
const CommunityDetailPage = lazy(() =>
  import("Pages/User/Communities/CommunityDetailPage")
);
const MeetingsPage = lazy(() => import("Pages/User/Meetings/MeetingsPage"));
const IntegrationsPage = lazy(() =>
  import("Pages/User/Integrations/IntegrationsPage")
);
const PlansPage = lazy(() => import("Pages/User/Plans/PlansPage"));
const MeetingDetailPage = lazy(() =>
  import("Pages/User/Meetings/MeetingDetailPage")
);
const AddReferralPage = lazy(() =>
  import("Pages/User/Referrals/AddReferralPage")
);
const EditReferralPage = lazy(() =>
  import("Pages/User/Referrals/EditReferralPage")
);
const RecommendationsPage = lazy(() =>
  import("Pages/User/Recommendations/RecommendationsPage")
);
import MagicLoginPage from "Pages/User/Auth/MagicLoginPage";
import SuperAdminLoginPage from "Pages/SuperAdmin/Auth/SuperAdminLoginPage";
import ReferralPaymentPage from "Pages/User/Referrals/ReferralPaymentPage";
import EditProfilePage from "Pages/User/Profile/EditProfilePage";
import PaymentStatusPage from "Pages/User/Referrals/PaymentStatusPage";
import CommunityChat from "Pages/User/Communities/CommunityChat";

export const DynamicWrapper = ({ isAuthenticated, role, children }) => {
  console.log("role",role)
  if (!isAuthenticated) {
    return <PublicWrapper>{children}</PublicWrapper>;
  }
  if (isAuthenticated) {
    if (["member", "user"].includes(role)) {
      return <UserWrapper>{children}</UserWrapper>;
    }

    if (["super_admin"].includes(role)) {
      return <AdminWrapper>{children}</AdminWrapper>;
    }
  }
};

export const NotFound = ({ isAuthenticated, role }) => {
  if (!isAuthenticated) {
    return (
      <PublicWrapper>
        <NotFoundPage />
      </PublicWrapper>
    );
  }
  if (isAuthenticated) {
    if (["member", "user"].includes(role)) {
      return (
        <UserWrapper>
          <NotFoundPage />
        </UserWrapper>
      );
    }

    if (role === "super_admin") {
      return (
        <AdminWrapper>
          <NotFoundPage />
        </AdminWrapper>
      );
    }
  }
};

export default () => {
  const { state } = useContext(AuthContext);
  const { globalState } = useContexts();
  const navigate = useNavigate();

  return (
    <>
      <LazyLoad>
        <SnackBar />
        <SessionExpiredModal />
        <RouteChangeModal
          isOpen={globalState?.showRouteChange}
          routes={[
            ...(state?.isAuthenticated
              ? []
              : [
                  { name: "Admin Login", route: "/admin/login" },
                  { name: "User Login", route: "/member/login" },
                ]),
          ]}
          title="Change Route"
        />
      </LazyLoad>

      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<PublicPage />} />

        {/* Auth Routes */}
        {/* <Route path="/admin/login" element={<AdminLoginPage />} /> */}
        <Route
          path="/member/login"
          element={
            <Suspense fallback={null}>
              <LazyLoad>
                <UserLoginPage />
              </LazyLoad>
            </Suspense>
          }
        />
        <Route path="/member/signup" element={<UserSignUpPage />} />
        <Route path="/member/forgot" element={<ForgotPasswordPage />} />
        <Route path="/member/reset" element={<UserResetPage />} />
        <Route
          path="/magic-login"
          element={  <CallbackPage />  }
            />
        {/* <Route path="/member/magic-login" element={<UserMagicLoginPage />} /> */}
        <Route
          path="/member/magic-login/verify"
          element={<MagicLoginVerifyPage />}
        />

        {/* Admin */}

        <Route
          exact
          path="/admin/user"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/user"
              element={
                <AdminWrapper>
                  <AdminListUserPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-user"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-user"
              element={
                <AdminWrapper>
                  <AdminAddUserPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-user/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-user/:id"
              element={
                <AdminWrapper>
                  <AdminEditUserPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-user/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-user/:id"
              element={
                <AdminWrapper>
                  <AdminViewUserPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-stripe_product"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-stripe_product"
              element={
                <AdminWrapper>
                  <AdminAddStripe_productPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-stripe_product/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-stripe_product/:id"
              element={
                <AdminWrapper>
                  <AdminEditStripe_productPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-stripe_product/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-stripe_product/:id"
              element={
                <AdminWrapper>
                  <AdminViewStripe_productPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-stripe_price"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-stripe_price"
              element={
                <AdminWrapper>
                  <AdminAddStripe_pricePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-stripe_price/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-stripe_price/:id"
              element={
                <AdminWrapper>
                  <AdminEditStripe_pricePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-stripe_price/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-stripe_price/:id"
              element={
                <AdminWrapper>
                  <AdminViewStripe_pricePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-community"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-community"
              element={
                <AdminWrapper>
                  <AdminAddCommunityPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-community/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-community/:id"
              element={
                <AdminWrapper>
                  <AdminEditCommunityPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-community/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-community/:id"
              element={
                <AdminWrapper>
                  <AdminViewCommunityPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/industry"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/industry"
              element={
                <AdminWrapper>
                  <AdminListIndustryPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/plans"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/plans"
              element={
                <AdminWrapper>
                  <AdminListPlansPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-plan/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-plan/:id"
              element={
                <AdminWrapper>
                  <AdminViewPlansPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/payments"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/payments"
              element={
                <AdminWrapper>
                  <AdminListPaymentsPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-payment/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-payment/:id"
              element={
                <AdminWrapper>
                  <AdminViewPaymentPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/settings"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/settings"
              element={
                <AdminWrapper>
                  <AdminSettingsPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-industry"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-industry"
              element={
                <AdminWrapper>
                  <AdminAddIndustryPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-industry/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-industry/:id"
              element={
                <AdminWrapper>
                  <AdminEditIndustryPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-industry/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-industry/:id"
              element={
                <AdminWrapper>
                  <AdminViewIndustryPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/community"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/community"
              element={
                <AdminWrapper>
                  <AdminListCommunityPage />
                </AdminWrapper>
              }
            />
          }
        />

<Route
          exact
          path="/admin/commission"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/commission"
              element={
                <AdminWrapper>
                  <AdminListCommissionPage />
                </AdminWrapper>
              }
            />
          }
        />

<Route
          exact
          path="/admin/view-commission/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-commission/:id"
              element={
                <AdminWrapper>
                  <AdminViewCommissionPage />
                </AdminWrapper>
              }
            />
          }
        />



        <Route
          exact
          path="/admin/add-community_member"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-community_member"
              element={
                <AdminWrapper>
                  <AdminAddCommunity_memberPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-community_member/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-community_member/:id"
              element={
                <AdminWrapper>
                  <AdminEditCommunity_memberPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-community_member/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-community_member/:id"
              element={
                <AdminWrapper>
                  <AdminViewCommunity_memberPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-community_join_request/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-community_join_request/:id"
              element={
                <AdminWrapper>
                  <AdminViewCommunity_join_requestPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-community_invite"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-community_invite"
              element={
                <AdminWrapper>
                  <AdminAddCommunity_invitePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-community_invite/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-community_invite/:id"
              element={
                <AdminWrapper>
                  <AdminEditCommunity_invitePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-community_invite/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-community_invite/:id"
              element={
                <AdminWrapper>
                  <AdminViewCommunity_invitePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/referral"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/referral"
              element={
                <AdminWrapper>
                  <AdminListReferralPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-referral"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-referral"
              element={
                <AdminWrapper>
                  <AdminAddReferralPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-referral/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-referral/:id"
              element={
                <AdminWrapper>
                  <AdminEditReferralPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-referral/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-referral/:id"
              element={
                <AdminWrapper>
                  <AdminViewReferralPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/referral_communities"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/referral_communities"
              element={
                <AdminWrapper>
                  <AdminListReferral_communitiesPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-referral_communities"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-referral_communities"
              element={
                <AdminWrapper>
                  <AdminAddReferral_communitiesPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-referral_communities/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-referral_communities/:id"
              element={
                <AdminWrapper>
                  <AdminEditReferral_communitiesPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-referral_communities/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-referral_communities/:id"
              element={
                <AdminWrapper>
                  <AdminViewReferral_communitiesPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/meetings"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/meetings"
              element={
                <AdminWrapper>
                  <AdminListMeetingsPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-meetings"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-meetings"
              element={
                <AdminWrapper>
                  <AdminAddMeetingsPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-meetings/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-meetings/:id"
              element={
                <AdminWrapper>
                  <AdminEditMeetingsPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-meetings/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-meetings/:id"
              element={
                <AdminWrapper>
                  <AdminViewMeetingsPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/meeting_attendee"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/meeting_attendee"
              element={
                <AdminWrapper>
                  <AdminListMeeting_attendeePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-meeting_attendee"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-meeting_attendee"
              element={
                <AdminWrapper>
                  <AdminAddMeeting_attendeePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-meeting_attendee/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-meeting_attendee/:id"
              element={
                <AdminWrapper>
                  <AdminEditMeeting_attendeePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-meeting_attendee/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-meeting_attendee/:id"
              element={
                <AdminWrapper>
                  <AdminViewMeeting_attendeePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/integration"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/integration"
              element={
                <AdminWrapper>
                  <AdminListIntegrationPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-integration"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/add-integration"
              element={
                <AdminWrapper>
                  <AdminAddIntegrationPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-integration/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/edit-integration/:id"
              element={
                <AdminWrapper>
                  <AdminEditIntegrationPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-integration/:id"
          element={
            <PrivateRoute
              access="super_admin"
              path="/admin/view-integration/:id"
              element={
                <AdminWrapper>
                  <AdminViewIntegrationPage />
                </AdminWrapper>
              }
            />
          }
        />
        {/* Admin end */}

        {/* User Dashboard Routes */}
        <Route
          path="/member/dashboard"
          element={
            <PrivateRoute
              path="/member/dashboard"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <UserDashboardPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Profile Routes */}
        <Route
          path="/member/profile"
          element={
            <PrivateRoute
              path="/member/profile"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <UserProfilePage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/profile/settings"
          element={
            <PrivateRoute
              path="/member/profile/settings"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ProfileSettingsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/profile/edit"
          element={
            <PrivateRoute
              path="/member/profile/edit"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <EditProfilePage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Community Routes */}
        <Route
          path="/member/communities"
          element={
            <PrivateRoute
              path="/member/communities"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunitiesPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Callback Routes */}
        <Route
          path="/member/callback"
          element={
            <PublicRoute
              path="/member/callback"
              element={
                // <UserWrapper>
                <LazyLoad>
                  <CallbackPage />
                </LazyLoad>
                // </UserWrapper>
              }
            />
          }
        />
        <Route
          path="/login/oauth"
          element={
            <PublicRoute
              path="/login/oauth"
              element={
                // <UserWrapper>
                <LazyLoad>
                  <CallbackPage />
                </LazyLoad>
                // </UserWrapper>
              }
            />
          }
        />
        {/* <Route
          path="/member/magic-login"
          element={
            <PublicRoute
              path="/member/magic-login"
              element={
                // <UserWrapper>
                <LazyLoad>
                  <CallbackPage />
                </LazyLoad>
                // </UserWrapper>
              }
            />
          }
        /> */}
        {/* Referral Routes */}
        <Route
          path="/member/referrals"
          element={
            <PrivateRoute
              path="/member/referrals"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/add"
          element={
            <PrivateRoute
              path="/member/referrals/add"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <AddReferralPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/edit/:id"
          element={
            <PrivateRoute
              path="/member/referrals/edit/:id"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <EditReferralPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/form"
          element={
            <PrivateRoute
              path="/member/referrals/form"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralFormPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/feed"
          element={
            <PrivateRoute
              path="/member/referrals/feed"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralFeedPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/:id/details"
          element={
            <PrivateRoute
              path="/member/referrals/:id/details"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralDetailsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* <Route
          path="/member/referrals/:id/payment"
          element={
            <PrivateRoute
              path="/member/referrals/:id/payment"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralPaymentPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        /> */}

        <Route
          path="/member/referrals/:id/timeline"
          element={
            <PrivateRoute
              path="/member/referrals/:id/timeline"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralTimelinePage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/:id/detail"
          element={
            <PrivateRoute
              path="/member/referrals/:id/detail"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralDetailPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/:id/feed"
          element={
            <PrivateRoute
              path="/member/referrals/:id/feed"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralFeedDetailPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Community Routes */}
        <Route
          path="/member/communities/create"
          element={
            <PrivateRoute
              path="/member/communities/create"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CreateCommunityPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />
        <Route
          path="/member/communities/create"
          element={
            <PrivateRoute
              path="/member/communities/create"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CreateCommunityPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* <Route
          path="/member/communities/:id/members"
          element={
            <PrivateRoute
              path="/member/communities/:id/members"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunityMembersPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        /> */}

        <Route
          path="/member/communities/create/step-3"
          element={
            <PrivateRoute
              path="/member/communities/create/step-3"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunityCreateStepThreePage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/communities/create/privacy"
          element={
            <PrivateRoute
              path="/member/communities/create/privacy"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunityPrivacySettingsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/communities/upgrade"
          element={
            <PrivateRoute
              path="/member/communities/upgrade"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunityUpgradePage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/communities/form"
          element={
            <PrivateRoute
              path="/member/communities/form"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunityFormPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/communities/:id/requests"
          element={
            <PrivateRoute
              path="/member/communities/:id/requests"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunityRequestsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Payment Routes */}
        <Route
          path="/member/payment/dashboard"
          element={
            <PrivateRoute
              path="/member/payment/dashboard"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <PaymentDashboardPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/payment/billing"
          element={
            <PrivateRoute
              path="/member/payment/billing"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <BillingPlanPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/payment/modal"
          element={
            <PrivateRoute
              path="/member/payment/modal"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <PaymentModalPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Settings Routes */}
        <Route
          path="/member/settings"
          element={
            <PrivateRoute
              path="/member/settings"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <UserSettingsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Chat Routes */}
        <Route
          path="/member/chats"
          element={
            <PrivateRoute
              path="/member/chats"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ChatPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/chat/:id"
          element={
            <PrivateRoute
              path="/member/chat/:id"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ChatPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Community Chat Route */}
        <Route
          path="/member/communities/:id/chat"
          element={
            <PrivateRoute
              path="/member/communities/:id/chat"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunityChat />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Affiliate Routes */}
        <Route
          path="/member/affiliate"
          element={
            <PrivateRoute
              path="/member/affiliate"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <AffiliateProgramPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Auth Routes */}
        <Route
          path="/member/forgot-password"
          element={
            <PrivateRoute
              path="/member/forgot-password"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ForgotPasswordPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/signup"
          element={
            <PublicRoute
              path="/member/signup"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <UserSignUpPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        {/* Admin Routes */}
        {/* ... existing admin routes ... */}

        <Route
          path="/member/communities/:id"
          element={
            <PrivateRoute
              path="/member/communities/:id"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CommunityDetailPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/:id/referral-recommendations"
          element={
            <PrivateRoute
              path="/member/referrals/:id/referral-recommendations"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralRecommendPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/meetings"
          element={
            <PrivateRoute
              path="/member/meetings"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <MeetingsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/integrations"
          access="member"
          element={
            <PrivateRoute
              path="/member/integrations"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <IntegrationsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />
        <Route
          path="/member/integration"
          access="member"
          element={
            <PrivateRoute
              path="/member/integration"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <IntegrationsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/plans"
          element={
            <PrivateRoute
              path="/member/plans"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <PlansPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/notes"
          element={
            <PrivateRoute
              path="/member/notes"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <NotesPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/recommendations"
          element={
            <PrivateRoute
              path="/member/recommendations"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <RecommendationsPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/communities/join"
          element={
            <PrivateRoute
              path="/member/communities/join"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <JoinCommunityPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/meetings/:id"
          element={
            <PrivateRoute
              path="/member/meetings/:id"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <MeetingDetailPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/communities/edit/:id"
          element={
            <PrivateRoute
              path="/member/communities/edit/:id"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <CreateCommunityPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />
        {/* 404 Route */}
        <Route
          path="*"
          element={
            <NotFound
              isAuthenticated={state?.isAuthenticated}
              role={state?.role}
            />
          }
        />
        <Route
          path="/member/magic-login-page"
          element={
            <PublicRoute
              path="/member/magic-login-page"
              element={
                <Suspense fallback={null}>
                  <LazyLoad>
                    <MagicLoginPage />
                  </LazyLoad>
                </Suspense>
              }
            />
          }
        />
        <Route path="/admin/login" element={<SuperAdminLoginPage />} />

        {/* Add both payment routes */}
        <Route
          path="/member/referrals/:referral_id/payment"
          element={
            <PrivateRoute
              path="/member/referrals/:referral_id/payment"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <PaymentStatusPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/referrals/:id/payment-status"
          element={
            <PrivateRoute
              path="/member/referrals/:id/payment-status"
              access="member"
              element={
                <UserWrapper>
                  <Suspense fallback={null}>
                    <LazyLoad>
                      <ReferralPaymentPage />
                    </LazyLoad>
                  </Suspense>
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/member/user/:user_id"
          element={
            <UserWrapper>
              <Suspense fallback={null}>
                <LazyLoad>
                  <ViewUserProfile />
                </LazyLoad>
              </Suspense>
            </UserWrapper>
          }
        />
      </Routes>
    </>
  );
};
